import { NextRequest, NextResponse } from 'next/server'
import { EnhancedStoreService } from '@/lib/enhanced-store-service'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { operation, data } = body

    if (!operation || !data) {
      return NextResponse.json(
        { error: 'Operation and data are required' },
        { status: 400 }
      )
    }

    let result

    switch (operation) {
      case 'CREATE':
        if (!data.stores || !Array.isArray(data.stores)) {
          return NextResponse.json(
            { error: 'stores array is required for CREATE operation' },
            { status: 400 }
          )
        }
        result = await EnhancedStoreService.bulkCreateStores(data)
        break

      case 'UPDATE':
        if (!data.updates || !Array.isArray(data.updates)) {
          return NextResponse.json(
            { error: 'updates array is required for UPDATE operation' },
            { status: 400 }
          )
        }
        result = await EnhancedStoreService.bulkUpdateStores(data)
        break

      case 'DELETE':
        if (!data.storeIds || !Array.isArray(data.storeIds)) {
          return NextResponse.json(
            { error: 'storeIds array is required for DELETE operation' },
            { status: 400 }
          )
        }
        result = await EnhancedStoreService.bulkDeleteStores(data)
        break

      default:
        return NextResponse.json(
          { error: 'Invalid operation. Must be CREATE, UPDATE, or DELETE' },
          { status: 400 }
        )
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Error in bulk store operation:', error)
    return NextResponse.json(
      { error: 'Internal server error during bulk operation' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const operationId = searchParams.get('operationId')

    if (!operationId) {
      return NextResponse.json(
        { error: 'operationId is required' },
        { status: 400 }
      )
    }

    const result = await EnhancedStoreService.getBulkOperationStatus(operationId)
    
    if (!result) {
      return NextResponse.json(
        { error: 'Operation not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Error getting bulk operation status:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const operationId = searchParams.get('operationId')

    if (!operationId) {
      return NextResponse.json(
        { error: 'operationId is required' },
        { status: 400 }
      )
    }

    const success = await EnhancedStoreService.cancelBulkOperation(operationId)
    
    return NextResponse.json({ 
      success,
      message: success ? 'Operation cancelled' : 'Operation could not be cancelled'
    })
  } catch (error) {
    console.error('Error cancelling bulk operation:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
