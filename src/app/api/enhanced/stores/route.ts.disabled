import { NextRequest, NextResponse } from 'next/server'
import { EnhancedStoreService } from '@/lib/enhanced-store-service'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Parse filters
    const filters: Record<string, unknown> = {}

    if (searchParams.get('code')) filters.code = searchParams.get('code')
    if (searchParams.get('name')) filters.name = searchParams.get('name')
    if (searchParams.get('storeType')) {
      filters.storeType = searchParams.get('storeType')!.split(',')
    }
    if (searchParams.get('status')) {
      filters.status = searchParams.get('status')!.split(',')
    }
    if (searchParams.get('region')) {
      filters.region = searchParams.get('region')!.split(',')
    }
    if (searchParams.get('city')) {
      filters.city = searchParams.get('city')!.split(',')
    }
    if (searchParams.get('storeGroup')) filters.storeGroup = searchParams.get('storeGroup')
    if (searchParams.get('isOpen')) filters.isOpen = searchParams.get('isOpen') === 'true'
    if (searchParams.get('hasOrders')) filters.hasOrders = searchParams.get('hasOrders') === 'true'
    if (searchParams.get('parentStoreId')) {
      filters.parentStoreId = parseInt(searchParams.get('parentStoreId')!)
    }
    if (searchParams.get('searchTerm')) filters.searchTerm = searchParams.get('searchTerm')

    // Pagination
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const pagination = { page, limit }

    // Sorting
    const sortField = searchParams.get('sortField') || 'code'
    const sortDirection = (searchParams.get('sortDirection') || 'asc') as 'asc' | 'desc'
    const sorting = { field: sortField, direction: sortDirection }

    const result = await EnhancedStoreService.searchStores(filters, pagination, sorting)

    return NextResponse.json(result)
  } catch (error: unknown) {
    console.error('Error searching stores:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Error searching stores' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const storeData = await request.json()

    if (!storeData.code) {
      return NextResponse.json(
        { error: 'Store code is required' },
        { status: 400 }
      )
    }

    const store = await EnhancedStoreService.createStore(storeData)

    return NextResponse.json(store, { status: 201 })
  } catch (error: unknown) {
    console.error('Error creating store:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Error creating store' },
      { status: 500 }
    )
  }
}
