import { NextRequest, NextResponse } from 'next/server'
import { EnhancedStoreService } from '@/lib/enhanced-store-service'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { storeData, isUpdate = false } = body

    if (!storeData) {
      return NextResponse.json(
        { error: 'storeData is required' },
        { status: 400 }
      )
    }

    // Validate store data
    const result = await EnhancedStoreService.validateStoreData(storeData, isUpdate)

    return NextResponse.json(result)
  } catch (error) {
    console.error('Error validating store data:', error)
    return NextResponse.json(
      { error: 'Internal server error during validation' },
      { status: 500 }
    )
  }
}
