import { NextRequest, NextResponse } from 'next/server'
import { EnhancedCustomerService } from '@/lib/enhanced-customer-service'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Parse filters
    const filters: Record<string, unknown> = {}
    
    if (searchParams.get('name')) filters.name = searchParams.get('name')
    if (searchParams.get('customerType')) {
      filters.customerType = searchParams.get('customerType')!.split(',')
    }
    if (searchParams.get('status')) {
      filters.status = searchParams.get('status')!.split(',')
    }
    if (searchParams.get('segment')) {
      filters.segment = searchParams.get('segment')!.split(',')
    }
    if (searchParams.get('loyaltyTier')) {
      filters.loyaltyTier = searchParams.get('loyaltyTier')!.split(',')
    }
    if (searchParams.get('assignedSalesRep')) {
      filters.assignedSalesRep = searchParams.get('assignedSalesRep')
    }
    if (searchParams.get('city')) filters.city = searchParams.get('city')
    if (searchParams.get('state')) filters.state = searchParams.get('state')
    if (searchParams.get('hasOrders')) filters.hasOrders = searchParams.get('hasOrders') === 'true'
    if (searchParams.get('totalSpentMin')) {
      filters.totalSpentMin = parseFloat(searchParams.get('totalSpentMin')!)
    }
    if (searchParams.get('totalSpentMax')) {
      filters.totalSpentMax = parseFloat(searchParams.get('totalSpentMax')!)
    }
    if (searchParams.get('lastOrderAfter')) {
      filters.lastOrderAfter = new Date(searchParams.get('lastOrderAfter')!)
    }
    if (searchParams.get('lastOrderBefore')) {
      filters.lastOrderBefore = new Date(searchParams.get('lastOrderBefore')!)
    }
    if (searchParams.get('searchTerm')) filters.searchTerm = searchParams.get('searchTerm')
    
    // Pagination
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const pagination = { page, limit }
    
    // Sorting
    const sortField = searchParams.get('sortField') || 'name'
    const sortDirection = (searchParams.get('sortDirection') || 'asc') as 'asc' | 'desc'
    const sorting = { field: sortField, direction: sortDirection }
    
    const result = await EnhancedCustomerService.searchCustomers(filters, pagination, sorting)
    
    return NextResponse.json(result)
  } catch (error) {
    console.error('Error searching customers:', error)
    return NextResponse.json(
      { error: 'Error searching customers' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const customerData = await request.json()
    
    if (!customerData.name) {
      return NextResponse.json(
        { error: 'Customer name is required' },
        { status: 400 }
      )
    }
    
    const customer = await EnhancedCustomerService.createCustomer(customerData)
    
    return NextResponse.json(customer, { status: 201 })
  } catch (error) {
    console.error('Error creating customer:', error)
    return NextResponse.json(
      { error: 'Error creating customer' },
      { status: 500 }
    )
  }
}
