import { NextRequest, NextResponse } from 'next/server'
import { PricingService } from '@/lib/pricing-service'

export async function GET() {
  try {
    const storePricing = await PricingService.getAllStorePricing()
    return NextResponse.json(storePricing)
  } catch (error) {
    console.error('Error fetching store pricing:', error)
    return NextResponse.json(
      { error: 'Error fetching store pricing' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      storeCodeId,
      name,
      markupType,
      markupValue,
      serviceFee
    } = body

    // Validation
    if (!storeCodeId) {
      return NextResponse.json(
        { error: 'Store code ID is required' },
        { status: 400 }
      )
    }

    if (!name || !name.trim()) {
      return NextResponse.json(
        { error: 'Pricing configuration name is required' },
        { status: 400 }
      )
    }

    if (!markupType || !['PERCENTAGE', 'FIXED_AMOUNT'].includes(markupType)) {
      return NextResponse.json(
        { error: 'Invalid markup type. Must be PERCENTAGE or FIXED_AMOUNT' },
        { status: 400 }
      )
    }

    if (markupValue === undefined || markupValue === null || markupValue < 0) {
      return NextResponse.json(
        { error: 'Markup value must be a non-negative number' },
        { status: 400 }
      )
    }

    if (serviceFee === undefined || serviceFee === null || serviceFee < 0) {
      return NextResponse.json(
        { error: 'Pasabuy fee must be a non-negative number' },
        { status: 400 }
      )
    }

    const storePricing = await PricingService.createStorePricing({
      storeCodeId: parseInt(storeCodeId),
      name: name.trim(),
      serviceFee: parseFloat(serviceFee),
      pricingTiers: [
        {
          minPrice: 0,
          maxPrice: null,
          markupType: markupType as 'PERCENTAGE' | 'FIXED_AMOUNT',
          markupValue: parseFloat(markupValue),
          sortOrder: 0
        }
      ]
    })

    return NextResponse.json(storePricing, { status: 201 })
  } catch (error) {
    console.error('Error creating store pricing:', error)
    
    // Handle unique constraint violation
    if (error instanceof Error && error.message.includes('Unique constraint')) {
      return NextResponse.json(
        { error: 'A pricing configuration for this store already exists' },
        { status: 409 }
      )
    }

    return NextResponse.json(
      { error: 'Error creating store pricing' },
      { status: 500 }
    )
  }
}
