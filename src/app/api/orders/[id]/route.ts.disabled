import { prisma } from '@/lib/db'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const orderId = parseInt(id)

    if (isNaN(orderId)) {
      return NextResponse.json(
        { error: 'Invalid order ID' },
        { status: 400 }
      )
    }

    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        storeCode: true,
        customer: true
      }
    })

    if (!order) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(order)
  } catch (error) {
    console.error('Error fetching order:', error)
    return NextResponse.json(
      { error: 'Error fetching order' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const orderId = parseInt(id)

    if (isNaN(orderId)) {
      return NextResponse.json(
        { error: 'Invalid order ID' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const {
      productName,
      quantity,
      usageUnit,
      comment,
      storePrice,
      pasabuyFee,
      customerPrice,
      storeCodeId,
      customerId,
      isBought,
      packingStatus,
      imageFilename
    } = body

    // Build update data object
    const updateData: Record<string, unknown> = {}

    if (productName !== undefined) updateData.productName = productName.trim()
    if (quantity !== undefined) updateData.quantity = parseInt(quantity)
    if (usageUnit !== undefined) updateData.usageUnit = usageUnit?.trim() || null
    if (comment !== undefined) updateData.comment = comment?.trim() || null
    if (storePrice !== undefined) updateData.storePrice = parseFloat(storePrice)
    if (pasabuyFee !== undefined) updateData.pasabuyFee = parseFloat(pasabuyFee)
    if (customerPrice !== undefined) updateData.customerPrice = parseFloat(customerPrice)
    if (storeCodeId !== undefined) updateData.storeCodeId = storeCodeId ? parseInt(storeCodeId) : null
    if (customerId !== undefined) updateData.customerId = customerId ? parseInt(customerId) : null
    if (isBought !== undefined) updateData.isBought = isBought
    if (packingStatus !== undefined) updateData.packingStatus = packingStatus
    if (imageFilename !== undefined) updateData.imageFilename = imageFilename

    // Auto-calculate customer price if store price or pasabuy fee changed
    if ((storePrice !== undefined || pasabuyFee !== undefined) && customerPrice === undefined) {
      const currentOrder = await prisma.order.findUnique({ where: { id: orderId } })
      if (currentOrder) {
        const newStorePrice = storePrice !== undefined ? parseFloat(storePrice) : currentOrder.storePrice
        const newPasabuyFee = pasabuyFee !== undefined ? parseFloat(pasabuyFee) : currentOrder.pasabuyFee
        updateData.customerPrice = Number(newStorePrice) + Number(newPasabuyFee)
      }
    }

    const order = await prisma.order.update({
      where: { id: orderId },
      data: updateData,
      include: {
        storeCode: true,
        customer: true
      }
    })

    // Note: Immediate auto-invoice generation disabled to prevent invoice fragmentation
    // Daily batching system will handle invoice generation at end of day
    // Manual generation is still available through the packing interface

    return NextResponse.json(order)
  } catch (error: unknown) {
    if (error && typeof error === 'object' && 'code' in error && error.code === 'P2025') {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      )
    }

    console.error('Error updating order:', error)
    return NextResponse.json(
      { error: 'Error updating order' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const orderId = parseInt(id)

    if (isNaN(orderId)) {
      return NextResponse.json(
        { error: 'Invalid order ID' },
        { status: 400 }
      )
    }

    await prisma.order.delete({
      where: { id: orderId }
    })

    return NextResponse.json({ success: true })
  } catch (error: unknown) {
    if (error && typeof error === 'object' && 'code' in error && error.code === 'P2025') {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      )
    }

    console.error('Error deleting order:', error)
    return NextResponse.json(
      { error: 'Error deleting order' },
      { status: 500 }
    )
  }
}
