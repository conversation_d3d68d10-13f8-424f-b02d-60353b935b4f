import { prisma } from '@/lib/db'
import { NextRequest, NextResponse } from 'next/server'

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { orderIds, updates } = body

    // Validate input
    if (!Array.isArray(orderIds) || orderIds.length === 0) {
      return NextResponse.json(
        { error: 'Order IDs array is required and cannot be empty' },
        { status: 400 }
      )
    }

    if (!updates || typeof updates !== 'object') {
      return NextResponse.json(
        { error: 'Updates object is required' },
        { status: 400 }
      )
    }

    // Validate order IDs are numbers
    const validOrderIds = orderIds.filter(id => typeof id === 'number' && !isNaN(id))
    if (validOrderIds.length === 0) {
      return NextResponse.json(
        { error: 'No valid order IDs provided' },
        { status: 400 }
      )
    }

    // Build update data object
    const updateData: Record<string, unknown> = {}

    if (updates.isBought !== undefined) {
      updateData.isBought = Boolean(updates.isBought)
    }

    if (updates.packingStatus !== undefined) {
      updateData.packingStatus = String(updates.packingStatus)
    }

    // If no valid updates provided
    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { error: 'No valid updates provided' },
        { status: 400 }
      )
    }

    // Get current orders to check their status and provide feedback
    const currentOrders = await prisma.order.findMany({
      where: {
        id: { in: validOrderIds }
      },
      select: {
        id: true,
        isBought: true,
        packingStatus: true
      }
    })

    // Calculate which orders will actually be updated
    let ordersToUpdate = currentOrders
    const skippedOrders = {
      alreadyBought: 0,
      alreadyPacked: 0,
      notFound: validOrderIds.length - currentOrders.length
    }

    // Filter orders based on the update type to avoid unnecessary updates
    if (updates.isBought === true) {
      // Only update orders that are not already bought
      ordersToUpdate = currentOrders.filter(order => !order.isBought)
      skippedOrders.alreadyBought = currentOrders.filter(order => order.isBought).length
    }

    if (updates.packingStatus === 'Packed') {
      // Only update orders that are not already packed
      ordersToUpdate = currentOrders.filter(order => order.packingStatus !== 'Packed')
      skippedOrders.alreadyPacked = currentOrders.filter(order => order.packingStatus === 'Packed').length
    }

    const orderIdsToUpdate = ordersToUpdate.map(order => order.id)

    // Generate bulk operation ID for tracking
    const bulkOperationId = `bulk_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // Perform bulk update using transaction for consistency
    const result = await prisma.$transaction(async (tx) => {
      if (orderIdsToUpdate.length > 0) {
        // First, perform the bulk update
        const updateResult = await tx.order.updateMany({
          where: {
            id: { in: orderIdsToUpdate }
          },
          data: updateData
        })

        // Get updated orders to return
        const updatedOrders = await tx.order.findMany({
          where: {
            id: { in: orderIdsToUpdate }
          },
          include: {
            storeCode: true,
            customer: true
          }
        })

        // Handle tracking in a more efficient way for bulk operations
        // Create status history records in bulk
        const statusHistoryRecords: any[] = []
        const timelineRecords: any[] = []

        for (const order of ordersToUpdate) {
          // Track isBought changes
          if ('isBought' in updateData && updateData.isBought !== order.isBought) {
            statusHistoryRecords.push({
              orderId: order.id,
              fromStatus: order.isBought ? 'bought' : 'pending',
              toStatus: updateData.isBought ? 'bought' : 'pending',
              statusType: 'PURCHASE_STATUS',
              changedBy: 'system',
              changeReason: 'Bulk operation',
              metadata: { bulkOperationId }
            })

            timelineRecords.push({
              orderId: order.id,
              eventType: updateData.isBought ? 'PURCHASED' : 'PURCHASE_CANCELLED',
              performedBy: 'system',
              notes: 'Bulk operation',
              eventData: { bulkOperationId }
            })
          }

          // Track packingStatus changes
          if ('packingStatus' in updateData && updateData.packingStatus !== order.packingStatus) {
            statusHistoryRecords.push({
              orderId: order.id,
              fromStatus: order.packingStatus,
              toStatus: String(updateData.packingStatus),
              statusType: 'PACKING_STATUS',
              changedBy: 'system',
              changeReason: 'Bulk operation',
              metadata: { bulkOperationId }
            })

            let eventType = 'SYSTEM_UPDATE'
            if (updateData.packingStatus === 'Packed') {
              eventType = 'PACKED'
            } else if (order.packingStatus === 'Packed') {
              eventType = 'PACKING_CANCELLED'
            }

            timelineRecords.push({
              orderId: order.id,
              eventType,
              performedBy: 'system',
              notes: 'Bulk operation',
              eventData: { bulkOperationId }
            })
          }
        }

        // Bulk insert tracking records if any exist
        if (statusHistoryRecords.length > 0) {
          await tx.orderStatusHistory.createMany({
            data: statusHistoryRecords
          })
        }

        if (timelineRecords.length > 0) {
          await tx.orderTimeline.createMany({
            data: timelineRecords
          })
        }

        // Note: Order metrics calculation is skipped for bulk operations to improve performance
        // Metrics can be recalculated later via a background job if needed

        return {
          updatedCount: updateResult.count,
          updatedOrders,
          skippedOrders,
          bulkOperationId
        }
      } else {
        return {
          updatedCount: 0,
          updatedOrders: [],
          skippedOrders,
          bulkOperationId
        }
      }
    })

    // Note: Immediate auto-invoice generation disabled to prevent invoice fragmentation
    // Daily batching system will handle invoice generation at end of day
    // Manual generation is still available through the packing interface

    return NextResponse.json({
      success: true,
      message: `Successfully updated ${result.updatedCount} order${result.updatedCount !== 1 ? 's' : ''}`,
      updatedCount: result.updatedCount,
      updatedOrders: result.updatedOrders,
      skippedOrders: result.skippedOrders,
      totalRequested: validOrderIds.length,
      bulkOperationId: result.bulkOperationId
    })

  } catch (error) {
    console.error('Error in bulk update:', error)
    return NextResponse.json(
      { error: 'Internal server error during bulk update' },
      { status: 500 }
    )
  }
}
