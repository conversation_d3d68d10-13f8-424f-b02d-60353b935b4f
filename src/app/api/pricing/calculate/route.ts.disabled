import { NextRequest, NextResponse } from 'next/server'
import { PricingService } from '@/lib/pricing-service'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { storePrice, storeCodeId } = body

    // Validation
    if (storePrice === undefined || storePrice === null) {
      return NextResponse.json(
        { error: 'Store price is required' },
        { status: 400 }
      )
    }

    const price = parseFloat(storePrice)
    if (isNaN(price) || price < 0) {
      return NextResponse.json(
        { error: 'Store price must be a valid positive number' },
        { status: 400 }
      )
    }

    // Calculate customer price using store-based pricing service
    const result = await PricingService.calculateCustomerPrice(
      price, 
      storeCodeId ? parseInt(storeCodeId) : undefined
    )

    return NextResponse.json(result)
  } catch (error) {
    console.error('Error calculating customer price:', error)
    return NextResponse.json(
      { error: 'Error calculating customer price' },
      { status: 500 }
    )
  }
}
