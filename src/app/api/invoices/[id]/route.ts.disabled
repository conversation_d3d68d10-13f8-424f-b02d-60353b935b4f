import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const invoiceId = parseInt(id)

    if (isNaN(invoiceId)) {
      return NextResponse.json(
        { error: 'Invalid invoice ID' },
        { status: 400 }
      )
    }

    const invoice = await prisma.invoice.findUnique({
      where: { id: invoiceId },
      include: {
        customer: true,
        invoiceItems: {
          include: {
            order: {
              include: {
                storeCode: true
              }
            }
          }
        }
      }
    })

    if (!invoice) {
      return NextResponse.json(
        { error: 'Invoice not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(invoice)
  } catch (error) {
    console.error('Error fetching invoice:', error)
    return NextResponse.json(
      { error: 'Error fetching invoice' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const invoiceId = parseInt(id)

    if (isNaN(invoiceId)) {
      return NextResponse.json(
        { error: 'Invalid invoice ID' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const { status, dueDate, paidDate, notes, orders } = body

    // Check if this is a full invoice update (with orders) or just a status/details update
    if (orders !== undefined) {
      // Full invoice update with line items
      if (!Array.isArray(orders) || orders.length === 0) {
        return NextResponse.json(
          { error: 'At least one order is required' },
          { status: 400 }
        )
      }

      // Fetch order details to calculate totals and validate
      const orderIds = orders.map((order: { orderId: number }) => order.orderId)
      const orderDetails = await prisma.order.findMany({
        where: {
          id: { in: orderIds }
        }
      })

      // Calculate totals and prepare invoice items data
      let subtotal = 0
      const invoiceItemsData = orders.map((invoiceOrder: { orderId: number; quantity?: number }) => {
        const order = orderDetails.find(o => o.id === invoiceOrder.orderId)
        if (!order) {
          throw new Error(`Order with ID ${invoiceOrder.orderId} not found`)
        }

        const quantity = invoiceOrder.quantity || 1
        if (quantity < 1) {
          throw new Error('Quantity must be at least 1')
        }

        const unitPrice = order.customerPrice
        const totalPrice = quantity * unitPrice

        subtotal += totalPrice

        return {
          orderId: invoiceOrder.orderId,
          quantity,
          unitPrice,
          totalPrice
        }
      })

      const total = subtotal

      // Update invoice with new items (delete old items and create new ones)
      const updateData: Record<string, unknown> = {
        subtotal,
        total,
        invoiceItems: {
          deleteMany: {}, // Delete all existing invoice items
          create: invoiceItemsData // Create new invoice items
        }
      }

      if (status !== undefined) updateData.status = status
      if (dueDate !== undefined) updateData.dueDate = dueDate ? new Date(dueDate) : null
      if (paidDate !== undefined) updateData.paidDate = paidDate ? new Date(paidDate) : null
      if (notes !== undefined) updateData.notes = notes

      // If status is being set to PAID, automatically set paidDate
      if (status === 'PAID' && !paidDate) {
        updateData.paidDate = new Date()
      }

      const invoice = await prisma.invoice.update({
        where: { id: invoiceId },
        data: updateData,
        include: {
          customer: true,
          invoiceItems: {
            include: {
              order: {
                include: {
                  storeCode: true
                }
              }
            }
          }
        }
      })

      return NextResponse.json(invoice)
    } else {
      // Simple status/details update (existing functionality)
      const updateData: Record<string, unknown> = {}

      if (status !== undefined) updateData.status = status
      if (dueDate !== undefined) updateData.dueDate = dueDate ? new Date(dueDate) : null
      if (paidDate !== undefined) updateData.paidDate = paidDate ? new Date(paidDate) : null
      if (notes !== undefined) updateData.notes = notes

      // If status is being set to PAID, automatically set paidDate
      if (status === 'PAID' && !paidDate) {
        updateData.paidDate = new Date()
      }

      const invoice = await prisma.invoice.update({
        where: { id: invoiceId },
        data: updateData,
        include: {
          customer: true,
          invoiceItems: {
            include: {
              order: {
                include: {
                  storeCode: true
                }
              }
            }
          }
        }
      })

      return NextResponse.json(invoice)
    }
  } catch (error) {
    console.error('Error updating invoice:', error)
    return NextResponse.json(
      { error: 'Error updating invoice' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const invoiceId = parseInt(id)

    if (isNaN(invoiceId)) {
      return NextResponse.json(
        { error: 'Invalid invoice ID' },
        { status: 400 }
      )
    }

    await prisma.invoice.delete({
      where: { id: invoiceId }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting invoice:', error)
    return NextResponse.json(
      { error: 'Error deleting invoice' },
      { status: 500 }
    )
  }
}
