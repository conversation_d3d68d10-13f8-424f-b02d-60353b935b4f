import { prisma } from '@/lib/db'
import { NextRequest, NextResponse } from 'next/server'

export interface SearchResult {
  id: number
  productName: string
  quantity: number
  usageUnit?: string | null
  comment?: string | null
  imageFilename: string | null
  storePrice: number
  pasabuyFee: number
  customerPrice: number
  isBought: boolean
  packingStatus: string
  storeCode?: {
    id: number
    code: string
    name: string | null
  } | null
  customer?: {
    id: number
    name: string
  } | null
  createdAt: string
  updatedAt: string
  matchType: 'productName' | 'customer' | 'storeCode' | 'usageUnit' | 'comment'
  matchText: string
}

export interface GroupedSearchResults {
  productName: SearchResult[]
  customer: SearchResult[]
  storeCode: SearchResult[]
  usageUnit: SearchResult[]
  comment: SearchResult[]
  total: number
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')

    if (!query || query.trim().length === 0) {
      return NextResponse.json({
        productName: [],
        customer: [],
        storeCode: [],
        usageUnit: [],
        comment: [],
        total: 0
      } as GroupedSearchResults)
    }

    const searchTerm = query.trim()

    // Search orders by product name
    const productNameResults = await prisma.order.findMany({
      where: {
        productName: {
          contains: searchTerm
        }
      },
      include: {
        storeCode: true,
        customer: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 20 // Limit results per category
    })

    // Search orders by customer name
    const customerResults = await prisma.order.findMany({
      where: {
        customer: {
          name: {
            contains: searchTerm
          }
        }
      },
      include: {
        storeCode: true,
        customer: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 20
    })

    // Search orders by store code
    const storeCodeResults = await prisma.order.findMany({
      where: {
        storeCode: {
          code: {
            contains: searchTerm
          }
        }
      },
      include: {
        storeCode: true,
        customer: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 20
    })

    // Search orders by usage unit
    const usageUnitResults = await prisma.order.findMany({
      where: {
        usageUnit: {
          contains: searchTerm
        }
      },
      include: {
        storeCode: true,
        customer: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 20
    })

    // Search orders by comment
    const commentResults = await prisma.order.findMany({
      where: {
        comment: {
          contains: searchTerm
        }
      },
      include: {
        storeCode: true,
        customer: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 20
    })

    // Transform results to include match information
    const transformedProductNameResults: SearchResult[] = productNameResults.map(item => ({
      ...item,
      createdAt: item.createdAt.toISOString(),
      updatedAt: item.updatedAt.toISOString(),
      matchType: 'productName' as const,
      matchText: item.productName
    }))

    // Deduplicate customer results by customer ID to show only one result per customer
    const customerMap = new Map<number, SearchResult>()
    customerResults.forEach(order => {
      if (order.customer && !customerMap.has(order.customer.id)) {
        customerMap.set(order.customer.id, {
          ...order,
          createdAt: order.createdAt.toISOString(),
          updatedAt: order.updatedAt.toISOString(),
          matchType: 'customer' as const,
          matchText: order.customer.name
        })
      }
    })
    const transformedCustomerResults: SearchResult[] = Array.from(customerMap.values())

    // Deduplicate store code results by store code ID to show only one result per store
    const storeCodeMap = new Map<number, SearchResult>()
    storeCodeResults.forEach(order => {
      if (order.storeCode && !storeCodeMap.has(order.storeCode.id)) {
        storeCodeMap.set(order.storeCode.id, {
          ...order,
          createdAt: order.createdAt.toISOString(),
          updatedAt: order.updatedAt.toISOString(),
          matchType: 'storeCode' as const,
          matchText: order.storeCode.code
        })
      }
    })
    const transformedStoreCodeResults: SearchResult[] = Array.from(storeCodeMap.values())

    const transformedUsageUnitResults: SearchResult[] = usageUnitResults.map(order => ({
      ...order,
      createdAt: order.createdAt.toISOString(),
      updatedAt: order.updatedAt.toISOString(),
      matchType: 'usageUnit' as const,
      matchText: order.usageUnit || ''
    }))

    const transformedCommentResults: SearchResult[] = commentResults.map(order => ({
      ...order,
      createdAt: order.createdAt.toISOString(),
      updatedAt: order.updatedAt.toISOString(),
      matchType: 'comment' as const,
      matchText: order.comment || ''
    }))

    // Remove duplicates (orders that might appear in multiple categories)
    const seenIds = new Set<number>()
    const deduplicatedResults = {
      productName: transformedProductNameResults.filter(order => {
        if (seenIds.has(order.id)) return false
        seenIds.add(order.id)
        return true
      }),
      customer: transformedCustomerResults.filter(order => {
        if (seenIds.has(order.id)) return false
        seenIds.add(order.id)
        return true
      }),
      storeCode: transformedStoreCodeResults.filter(order => {
        if (seenIds.has(order.id)) return false
        seenIds.add(order.id)
        return true
      }),
      usageUnit: transformedUsageUnitResults.filter(order => {
        if (seenIds.has(order.id)) return false
        seenIds.add(order.id)
        return true
      }),
      comment: transformedCommentResults.filter(order => {
        if (seenIds.has(order.id)) return false
        seenIds.add(order.id)
        return true
      })
    }

    const total = deduplicatedResults.productName.length +
                  deduplicatedResults.customer.length +
                  deduplicatedResults.storeCode.length +
                  deduplicatedResults.usageUnit.length +
                  deduplicatedResults.comment.length

    const response: GroupedSearchResults = {
      ...deduplicatedResults,
      total
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error performing search:', error)
    return NextResponse.json(
      { error: 'Error performing search' },
      { status: 500 }
    )
  }
}
