'use client'

import { usePathname } from 'next/navigation'
import { ReactNode } from 'react'

interface ConditionalNavigationProps {
  children: ReactNode
}

// Routes where navigation should be hidden (authentication pages)
const AUTH_ROUTES = [
  '/auth/login',
  '/auth/register',
  '/auth/forgot-password',
  '/auth/reset-password',
  '/auth/verify-email'
]

// Routes where navigation should be hidden (management pages)
const MANAGEMENT_ROUTES = [
  // Customer management pages
  /^\/customers\/\d+$/,           // Customer detail/view page (/customers/[id])
  /^\/customers\/new$/,           // Add new customer page (/customers/new)
  /^\/customers\/\d+\/edit$/,     // Edit customer page (/customers/[id]/edit)
  
  // Store management pages
  /^\/stores\/\d+$/,              // Store detail/view page (/stores/[id])
  /^\/stores\/new$/,              // Add new store page (/stores/new)
  /^\/stores\/\d+\/edit$/,        // Edit store page (/stores/[id]/edit)
]

function shouldHideNavigation(pathname: string): boolean {
  // Check auth routes
  if (AUTH_ROUTES.some(route => pathname.startsWith(route))) {
    return true
  }
  
  // Check management routes using regex patterns
  if (MANAGEMENT_ROUTES.some(pattern => pattern.test(pathname))) {
    return true
  }
  
  return false
}

export function ConditionalNavigation({ children }: ConditionalNavigationProps) {
  const pathname = usePathname()
  const hideNavigation = shouldHideNavigation(pathname)
  
  if (hideNavigation) {
    // For auth and management pages, show only the main content without navigation
    return (
      <main className="flex-1 min-h-screen">
        {/* Extract just the main content from children */}
        {Array.isArray(children) 
          ? children.find((child: any) => child?.type === 'main' || child?.props?.className?.includes('container'))
          : children
        }
      </main>
    )
  }
  
  // For all other pages, show full navigation
  return <>{children}</>
}
