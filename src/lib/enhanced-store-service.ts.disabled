// Enhanced Store Management Service for PasaBuy Pal
// Note: This is a simplified version that works with the current database schema
// It can be extended when the database schema is updated to include enhanced fields
import { prisma } from './db'
import * as XLSX from 'xlsx'
import { z } from 'zod'

// Simplified enums that match the current schema capabilities
type StoreType = 'RETAIL' | 'WHOLESALE' | 'ONLINE' | 'MARKETPLACE' | 'WAREHOUSE' | 'DISTRIBUTION_CENTER' | 'FRANCHISE' | 'CORPORATE'
type StoreStatus = 'ACTIVE' | 'INACTIVE' | 'MAINTENANCE' | 'TEMPORARILY_CLOSED' | 'PERMANENTLY_CLOSED' | 'UNDER_CONSTRUCTION'
type StorePriority = 'LOW' | 'NORMAL' | 'HIGH' | 'PREMIUM'
type ConfigDataType = 'STRING' | 'NUMBER' | 'BOOLEAN' | 'JSON'

export interface EnhancedStoreData {
  // Basic information
  code: string
  name?: string
  storeType?: StoreType
  status?: StoreStatus
  
  // Store hierarchy
  parentStoreId?: number
  storeGroup?: string
  region?: string
  district?: string
  
  // Contact and location
  address?: string
  city?: string
  state?: string
  postalCode?: string
  country?: string
  phone?: string
  email?: string
  website?: string
  
  // Store management
  managerName?: string
  managerPhone?: string
  managerEmail?: string
  contactPerson?: string
  
  // Operational information
  operatingHours?: string
  timezone?: string
  isOpen?: boolean
  
  // Store configuration
  allowsPickup?: boolean
  allowsDelivery?: boolean
  deliveryRadius?: number
  minimumOrder?: number
  serviceFee?: number
  
  // Performance settings
  averageProcessingTime?: number
  capacity?: number
  priority?: StorePriority
  
  // Notes
  notes?: string
  internalNotes?: string
  specialInstructions?: string
  
  // External integration
  externalStoreId?: string
  apiEndpoint?: string
  apiKey?: string
}

export interface StoreConfigurationData {
  configKey: string
  configValue: string
  dataType?: ConfigDataType
  description?: string
  category?: string
  isActive?: boolean
  isSystem?: boolean
}

export interface StoreSearchFilters {
  code?: string
  name?: string
  storeType?: StoreType[]
  status?: StoreStatus[]
  region?: string[]
  city?: string[]
  storeGroup?: string
  isOpen?: boolean
  hasOrders?: boolean
  parentStoreId?: number
  searchTerm?: string
}

export interface StorePerformanceMetrics {
  storeId: number
  storeCode: string
  storeName: string
  totalOrders: number
  totalRevenue: number
  averageOrderValue: number
  averageProcessingTime?: number
  orderCompletionRate: number
  customerSatisfactionScore?: number
  utilizationRate: number
  efficiencyScore: number
}

// Bulk Operations Interfaces
export interface BulkStoreOperation {
  operationId: string
  operationType: 'CREATE' | 'UPDATE' | 'DELETE'
  totalItems: number
  processedItems: number
  successfulItems: number
  failedItems: number
  errors: BulkOperationError[]
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'CANCELLED'
  startedAt: Date
  completedAt?: Date
}

export interface BulkOperationError {
  itemIndex: number
  itemData?: any
  errorCode: string
  errorMessage: string
  field?: string
}

export interface BulkStoreCreateData {
  stores: EnhancedStoreData[]
  validateOnly?: boolean
  continueOnError?: boolean
}

export interface BulkStoreUpdateData {
  updates: Array<{
    storeId: number
    data: Partial<EnhancedStoreData>
  }>
  validateOnly?: boolean
  continueOnError?: boolean
}

export interface BulkStoreDeleteData {
  storeIds: number[]
  force?: boolean // Skip dependency checks
  validateOnly?: boolean
}

// Import/Export Interfaces
export interface StoreImportResult {
  operationId: string
  totalRows: number
  processedRows: number
  successfulRows: number
  failedRows: number
  errors: ImportError[]
  warnings: ImportWarning[]
  createdStores: any[]
  updatedStores: any[]
  skippedRows: number[]
}

export interface ImportError {
  row: number
  field?: string
  value?: any
  errorCode: string
  errorMessage: string
}

export interface ImportWarning {
  row: number
  field?: string
  value?: any
  warningCode: string
  warningMessage: string
}

export interface StoreExportOptions {
  format: 'CSV' | 'EXCEL'
  includeMetrics?: boolean
  includeConfigurations?: boolean
  filters?: StoreSearchFilters
  fields?: string[]
}

// Validation Interfaces
export interface ValidationRule {
  field: string
  type: 'REQUIRED' | 'FORMAT' | 'RANGE' | 'CUSTOM' | 'DEPENDENCY'
  message: string
  validator?: (value: any, data: any) => boolean | Promise<boolean>
  params?: any
}

export interface ValidationResult {
  isValid: boolean
  errors: ValidationError[]
  warnings: ValidationWarning[]
}

export interface ValidationError {
  field: string
  value?: any
  errorCode: string
  errorMessage: string
  severity: 'ERROR' | 'WARNING'
}

export interface ValidationWarning {
  field: string
  value?: any
  warningCode: string
  warningMessage: string
}

export class EnhancedStoreService {
  /**
   * Create enhanced store with full configuration
   */
  static async createStore(storeData: EnhancedStoreData): Promise<any> {
    const store = await prisma.storeCode.create({
      data: {
        code: storeData.code.trim().toUpperCase(),
        name: storeData.name?.trim() || null,
        storeType: storeData.storeType || 'RETAIL',
        status: storeData.status || 'ACTIVE',
        
        // Store hierarchy
        parentStoreId: storeData.parentStoreId || null,
        storeGroup: storeData.storeGroup?.trim() || null,
        region: storeData.region?.trim() || null,
        district: storeData.district?.trim() || null,
        
        // Contact and location
        address: storeData.address?.trim() || null,
        city: storeData.city?.trim() || null,
        state: storeData.state?.trim() || null,
        postalCode: storeData.postalCode?.trim() || null,
        country: storeData.country?.trim() || 'Philippines',
        phone: storeData.phone?.trim() || null,
        email: storeData.email?.trim().toLowerCase() || null,
        website: storeData.website?.trim() || null,
        
        // Store management
        managerName: storeData.managerName?.trim() || null,
        managerPhone: storeData.managerPhone?.trim() || null,
        managerEmail: storeData.managerEmail?.trim().toLowerCase() || null,
        contactPerson: storeData.contactPerson?.trim() || null,
        
        // Operational information
        operatingHours: storeData.operatingHours?.trim() || null,
        timezone: storeData.timezone?.trim() || 'Asia/Manila',
        isOpen: storeData.isOpen ?? true,
        
        // Store configuration
        allowsPickup: storeData.allowsPickup ?? true,
        allowsDelivery: storeData.allowsDelivery ?? true,
        deliveryRadius: storeData.deliveryRadius || null,
        minimumOrder: storeData.minimumOrder || 0,
        serviceFee: storeData.serviceFee || 0,
        
        // Performance settings
        averageProcessingTime: storeData.averageProcessingTime || null,
        capacity: storeData.capacity || null,
        priority: storeData.priority || 'NORMAL',
        
        // Notes
        notes: storeData.notes?.trim() || null,
        internalNotes: storeData.internalNotes?.trim() || null,
        specialInstructions: storeData.specialInstructions?.trim() || null,
        
        // External integration
        externalStoreId: storeData.externalStoreId?.trim() || null,
        apiEndpoint: storeData.apiEndpoint?.trim() || null,
        apiKey: storeData.apiKey?.trim() || null,
      }
    })

    return store
  }

  /**
   * Update store with metrics recalculation
   */
  static async updateStore(storeId: number, updateData: Partial<EnhancedStoreData>): Promise<any> {
    const store = await prisma.storeCode.update({
      where: { id: storeId },
      data: {
        ...updateData,
        updatedAt: new Date()
      }
    })

    // Recalculate store metrics
    await this.recalculateStoreMetrics(storeId)

    return store
  }

  /**
   * Add or update store configuration
   */
  static async setStoreConfiguration(
    storeId: number, 
    configData: StoreConfigurationData
  ): Promise<any> {
    return await prisma.storeConfiguration.upsert({
      where: {
        storeCodeId_configKey: {
          storeCodeId: storeId,
          configKey: configData.configKey
        }
      },
      update: {
        configValue: configData.configValue,
        dataType: configData.dataType || 'STRING',
        description: configData.description,
        category: configData.category,
        isActive: configData.isActive ?? true,
        updatedAt: new Date()
      },
      create: {
        storeCodeId: storeId,
        configKey: configData.configKey,
        configValue: configData.configValue,
        dataType: configData.dataType || 'STRING',
        description: configData.description,
        category: configData.category,
        isActive: configData.isActive ?? true,
        isSystem: configData.isSystem ?? false
      }
    })
  }

  /**
   * Get store configuration value
   */
  static async getStoreConfiguration(
    storeId: number, 
    configKey: string
  ): Promise<string | null> {
    const config = await prisma.storeConfiguration.findUnique({
      where: {
        storeCodeId_configKey: {
          storeCodeId: storeId,
          configKey
        }
      }
    })

    return config?.configValue || null
  }

  /**
   * Advanced store search with enhanced filters
   */
  static async searchStores(
    filters: StoreSearchFilters,
    pagination?: { page: number; limit: number },
    sorting?: { field: string; direction: 'asc' | 'desc' }
  ): Promise<{ stores: any[]; total: number; hasMore: boolean }> {
    const whereClause: any = {}

    // Basic filters
    if (filters.code) {
      whereClause.code = { contains: filters.code, mode: 'insensitive' }
    }
    if (filters.name) {
      whereClause.name = { contains: filters.name, mode: 'insensitive' }
    }
    if (filters.storeType && filters.storeType.length > 0) {
      whereClause.storeType = { in: filters.storeType }
    }
    if (filters.status && filters.status.length > 0) {
      whereClause.status = { in: filters.status }
    }
    if (filters.region && filters.region.length > 0) {
      whereClause.region = { in: filters.region }
    }
    if (filters.city && filters.city.length > 0) {
      whereClause.city = { in: filters.city }
    }
    if (filters.storeGroup) {
      whereClause.storeGroup = { contains: filters.storeGroup, mode: 'insensitive' }
    }
    if (filters.isOpen !== undefined) {
      whereClause.isOpen = filters.isOpen
    }
    if (filters.parentStoreId) {
      whereClause.parentStoreId = filters.parentStoreId
    }

    // Order-related filters
    if (filters.hasOrders !== undefined) {
      if (filters.hasOrders) {
        whereClause.orders = { some: {} }
      } else {
        whereClause.orders = { none: {} }
      }
    }

    // Search term
    if (filters.searchTerm) {
      const searchTerm = filters.searchTerm.trim()
      whereClause.OR = [
        { code: { contains: searchTerm, mode: 'insensitive' } },
        { name: { contains: searchTerm, mode: 'insensitive' } },
        { storeGroup: { contains: searchTerm, mode: 'insensitive' } },
        { region: { contains: searchTerm, mode: 'insensitive' } },
        { city: { contains: searchTerm, mode: 'insensitive' } },
        { managerName: { contains: searchTerm, mode: 'insensitive' } }
      ]
    }

    // Pagination
    const page = pagination?.page || 1
    const limit = pagination?.limit || 50
    const skip = (page - 1) * limit

    // Sorting
    const orderBy: any = {}
    if (sorting) {
      orderBy[sorting.field] = sorting.direction
    } else {
      orderBy.code = 'asc'
    }

    const [stores, total] = await Promise.all([
      prisma.storeCode.findMany({
        where: whereClause,
        include: {
          parentStore: true,
          childStores: true,
          orders: {
            take: 5,
            orderBy: { createdAt: 'desc' }
          },
          configurations: {
            where: { isActive: true }
          }
        },
        orderBy,
        skip,
        take: limit
      }),
      prisma.storeCode.count({ where: whereClause })
    ])

    return {
      stores,
      total,
      hasMore: skip + stores.length < total
    }
  }

  /**
   * Recalculate store metrics
   */
  static async recalculateStoreMetrics(storeId: number): Promise<void> {
    const store = await prisma.storeCode.findUnique({
      where: { id: storeId },
      include: {
        orders: true
      }
    })

    if (!store) return

    const orders = store.orders
    const totalOrders = orders.length
    const totalRevenue = orders.reduce((sum, order) => sum + order.customerPrice, 0)
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0

    await prisma.storeCode.update({
      where: { id: storeId },
      data: {
        totalOrders,
        totalRevenue,
        averageOrderValue
      }
    })
  }

  /**
   * Get store performance metrics
   */
  static async getStorePerformanceMetrics(storeId: number): Promise<StorePerformanceMetrics> {
    const store = await prisma.storeCode.findUnique({
      where: { id: storeId },
      include: {
        orders: {
          include: {
            metrics: true
          }
        }
      }
    })

    if (!store) {
      throw new Error('Store not found')
    }

    const orders = store.orders
    const totalOrders = orders.length
    const totalRevenue = orders.reduce((sum, order) => sum + order.customerPrice, 0)
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0

    // Calculate average processing time from order metrics
    const processingTimes = orders
      .map(order => order.metrics?.totalProcessingTime)
      .filter(time => time !== null && time !== undefined) as number[]
    
    const averageProcessingTime = processingTimes.length > 0
      ? processingTimes.reduce((a, b) => a + b, 0) / processingTimes.length
      : undefined

    // Calculate order completion rate
    const completedOrders = orders.filter(order => order.deliveryStatus === 'Delivered').length
    const orderCompletionRate = totalOrders > 0 ? (completedOrders / totalOrders) * 100 : 0

    // Calculate utilization rate
    const capacity = store.capacity || 100
    const utilizationRate = Math.min(100, (totalOrders / capacity) * 100)

    // Calculate efficiency score
    const timeEfficiency = averageProcessingTime 
      ? Math.max(0, 100 - (averageProcessingTime / (24 * 60)) * 50) 
      : 50
    const completionEfficiency = orderCompletionRate
    const efficiencyScore = (timeEfficiency + completionEfficiency) / 2

    return {
      storeId: store.id,
      storeCode: store.code,
      storeName: store.name || store.code,
      totalOrders,
      totalRevenue,
      averageOrderValue,
      averageProcessingTime,
      orderCompletionRate,
      utilizationRate,
      efficiencyScore
    }
  }

  /**
   * Get store hierarchy
   */
  static async getStoreHierarchy(rootStoreId?: number): Promise<any[]> {
    const whereClause = rootStoreId 
      ? { parentStoreId: rootStoreId }
      : { parentStoreId: null }

    const stores = await prisma.storeCode.findMany({
      where: whereClause,
      include: {
        childStores: {
          include: {
            childStores: true
          }
        }
      },
      orderBy: { code: 'asc' }
    })

    return stores
  }

  /**
   * Get store analytics
   */
  static async getStoreAnalytics(filters?: StoreSearchFilters): Promise<{
    totalStores: number
    activeStores: number
    totalRevenue: number
    averageRevenue: number
    typeBreakdown: Array<{ type: string; count: number; revenue: number }>
    regionBreakdown: Array<{ region: string; count: number; revenue: number }>
    statusBreakdown: Array<{ status: string; count: number }>
    performanceMetrics: {
      topPerformingStores: Array<{ storeCode: string; revenue: number; orders: number }>
      underperformingStores: Array<{ storeCode: string; revenue: number; orders: number }>
    }
  }> {
    const whereClause = filters ? this.buildWhereClause(filters) : {}

    const [
      totalStores,
      activeStores,
      revenueAgg,
      typeStats,
      regionStats,
      statusStats,
      topStores,
      bottomStores
    ] = await Promise.all([
      prisma.storeCode.count({ where: whereClause }),
      prisma.storeCode.count({ where: { ...whereClause, status: 'ACTIVE' } }),
      prisma.storeCode.aggregate({
        where: whereClause,
        _sum: { totalRevenue: true },
        _avg: { totalRevenue: true }
      }),
      prisma.storeCode.groupBy({
        by: ['storeType'],
        where: whereClause,
        _count: true,
        _sum: { totalRevenue: true }
      }),
      prisma.storeCode.groupBy({
        by: ['region'],
        where: { ...whereClause, region: { not: null } },
        _count: true,
        _sum: { totalRevenue: true }
      }),
      prisma.storeCode.groupBy({
        by: ['status'],
        where: whereClause,
        _count: true
      }),
      prisma.storeCode.findMany({
        where: whereClause,
        orderBy: { totalRevenue: 'desc' },
        take: 5,
        select: { code: true, totalRevenue: true, totalOrders: true }
      }),
      prisma.storeCode.findMany({
        where: { ...whereClause, totalRevenue: { gt: 0 } },
        orderBy: { totalRevenue: 'asc' },
        take: 5,
        select: { code: true, totalRevenue: true, totalOrders: true }
      })
    ])

    return {
      totalStores,
      activeStores,
      totalRevenue: revenueAgg._sum.totalRevenue || 0,
      averageRevenue: revenueAgg._avg.totalRevenue || 0,
      typeBreakdown: typeStats.map(stat => ({
        type: stat.storeType,
        count: stat._count,
        revenue: stat._sum.totalRevenue || 0
      })),
      regionBreakdown: regionStats.map(stat => ({
        region: stat.region || 'Unknown',
        count: stat._count,
        revenue: stat._sum.totalRevenue || 0
      })),
      statusBreakdown: statusStats.map(stat => ({
        status: stat.status,
        count: stat._count
      })),
      performanceMetrics: {
        topPerformingStores: topStores.map(store => ({
          storeCode: store.code,
          revenue: store.totalRevenue,
          orders: store.totalOrders
        })),
        underperformingStores: bottomStores.map(store => ({
          storeCode: store.code,
          revenue: store.totalRevenue,
          orders: store.totalOrders
        }))
      }
    }
  }

  /**
   * Build where clause from filters (helper method)
   */
  private static buildWhereClause(filters: StoreSearchFilters): any {
    const whereClause: any = {}

    if (filters.storeType && filters.storeType.length > 0) {
      whereClause.storeType = { in: filters.storeType }
    }
    if (filters.status && filters.status.length > 0) {
      whereClause.status = { in: filters.status }
    }
    if (filters.region && filters.region.length > 0) {
      whereClause.region = { in: filters.region }
    }
    if (filters.isOpen !== undefined) {
      whereClause.isOpen = filters.isOpen
    }

    return whereClause
  }

  // ===== VALIDATION & BUSINESS RULES =====

  /**
   * Validation schema for store data
   */
  private static storeValidationSchema = z.object({
    code: z.string()
      .min(1, 'Store code is required')
      .max(20, 'Store code must be 20 characters or less')
      .regex(/^[A-Z0-9_-]+$/, 'Store code must contain only uppercase letters, numbers, underscores, and hyphens'),
    name: z.string().max(255, 'Store name must be 255 characters or less').optional(),
    storeType: z.enum(['RETAIL', 'WHOLESALE', 'ONLINE', 'MARKETPLACE', 'WAREHOUSE', 'DISTRIBUTION_CENTER', 'FRANCHISE', 'CORPORATE']).optional(),
    status: z.enum(['ACTIVE', 'INACTIVE', 'MAINTENANCE', 'TEMPORARILY_CLOSED', 'PERMANENTLY_CLOSED', 'UNDER_CONSTRUCTION']).optional(),
    parentStoreId: z.number().int().positive().optional(),
    storeGroup: z.string().max(100, 'Store group must be 100 characters or less').optional(),
    region: z.string().max(100, 'Region must be 100 characters or less').optional(),
    district: z.string().max(100, 'District must be 100 characters or less').optional(),
    address: z.string().max(500, 'Address must be 500 characters or less').optional(),
    city: z.string().max(100, 'City must be 100 characters or less').optional(),
    state: z.string().max(100, 'State must be 100 characters or less').optional(),
    postalCode: z.string().max(20, 'Postal code must be 20 characters or less').optional(),
    country: z.string().max(100, 'Country must be 100 characters or less').optional(),
    phone: z.string().max(20, 'Phone must be 20 characters or less').optional(),
    email: z.string().max(255, 'Email must be 255 characters or less').optional(),
    website: z.string().max(255, 'Website must be 255 characters or less').optional(),
    managerName: z.string().max(255, 'Manager name must be 255 characters or less').optional(),
    managerPhone: z.string().max(20, 'Manager phone must be 20 characters or less').optional(),
    managerEmail: z.string().max(255, 'Manager email must be 255 characters or less').optional(),
    contactPerson: z.string().max(255, 'Contact person must be 255 characters or less').optional(),
    operatingHours: z.string().max(500, 'Operating hours must be 500 characters or less').optional(),
    timezone: z.string().max(50, 'Timezone must be 50 characters or less').optional(),
    isOpen: z.boolean().optional(),
    allowsPickup: z.boolean().optional(),
    allowsDelivery: z.boolean().optional(),
    deliveryRadius: z.number().min(0, 'Delivery radius must be positive').max(1000, 'Delivery radius cannot exceed 1000km').optional(),
    minimumOrder: z.number().min(0, 'Minimum order must be positive').max(999999.99, 'Minimum order cannot exceed ₱999,999.99').optional(),
    serviceFee: z.number().min(0, 'Service fee must be positive').max(999999.99, 'Service fee cannot exceed ₱999,999.99').optional(),
    averageProcessingTime: z.number().int().min(1, 'Processing time must be at least 1 minute').max(10080, 'Processing time cannot exceed 1 week').optional(),
    capacity: z.number().int().min(1, 'Capacity must be at least 1').max(10000, 'Capacity cannot exceed 10,000').optional(),
    priority: z.enum(['LOW', 'NORMAL', 'HIGH', 'PREMIUM']).optional(),
    notes: z.string().max(1000, 'Notes must be 1000 characters or less').optional(),
    internalNotes: z.string().max(1000, 'Internal notes must be 1000 characters or less').optional(),
    specialInstructions: z.string().max(1000, 'Special instructions must be 1000 characters or less').optional(),
    externalStoreId: z.string().max(100, 'External store ID must be 100 characters or less').optional(),
    apiEndpoint: z.string().url('Invalid API endpoint URL').max(255, 'API endpoint must be 255 characters or less').optional(),
    apiKey: z.string().max(255, 'API key must be 255 characters or less').optional(),
  })

  /**
   * Business rules for store validation
   */
  private static businessRules: ValidationRule[] = [
    {
      field: 'code',
      type: 'REQUIRED',
      message: 'Store code is required'
    },
    {
      field: 'code',
      type: 'CUSTOM',
      message: 'Store code must be unique',
      validator: async (value: string, data: any) => {
        const existing = await prisma.storeCode.findFirst({
          where: {
            code: value.toUpperCase(),
            id: data.id ? { not: data.id } : undefined
          }
        })
        return !existing
      }
    },
    {
      field: 'parentStoreId',
      type: 'DEPENDENCY',
      message: 'Parent store must exist and cannot be the same as current store',
      validator: async (value: number, data: any) => {
        if (!value) return true
        if (data.id && value === data.id) return false
        const parent = await prisma.storeCode.findUnique({ where: { id: value } })
        return !!parent
      }
    },

    {
      field: 'deliveryRadius',
      type: 'DEPENDENCY',
      message: 'Delivery radius can only be set if delivery is allowed',
      validator: (value: number, data: any) => {
        if (!value) return true
        return data.allowsDelivery !== false
      }
    },
    {
      field: 'minimumOrder',
      type: 'DEPENDENCY',
      message: 'Minimum order should be reasonable for store type',
      validator: (value: number, data: any) => {
        if (!value) return true
        if (data.storeType === 'WHOLESALE' && value < 1000) return false
        if (data.storeType === 'RETAIL' && value > 10000) return false
        return true
      }
    }
  ]

  /**
   * Validate store data against schema and business rules
   */
  static async validateStoreData(data: Partial<EnhancedStoreData>, isUpdate = false): Promise<ValidationResult> {
    const errors: ValidationError[] = []
    const warnings: ValidationWarning[] = []

    try {
      // Schema validation
      if (isUpdate) {
        this.storeValidationSchema.partial().parse(data)
      } else {
        this.storeValidationSchema.parse(data)
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        for (const issue of error.issues) {
          errors.push({
            field: issue.path.join('.'),
            value: 'received' in issue ? issue.received : undefined,
            errorCode: issue.code,
            errorMessage: issue.message,
            severity: 'ERROR'
          })
        }
      }
    }

    // Business rules validation
    for (const rule of this.businessRules) {
      const fieldValue = (data as any)[rule.field]

      if (rule.type === 'REQUIRED' && !isUpdate && !fieldValue) {
        errors.push({
          field: rule.field,
          value: fieldValue,
          errorCode: 'REQUIRED',
          errorMessage: rule.message,
          severity: 'ERROR'
        })
      }

      if (fieldValue && rule.validator) {
        try {
          const isValid = await rule.validator(fieldValue, data)
          if (!isValid) {
            errors.push({
              field: rule.field,
              value: fieldValue,
              errorCode: rule.type,
              errorMessage: rule.message,
              severity: 'ERROR'
            })
          }
        } catch (error) {
          errors.push({
            field: rule.field,
            value: fieldValue,
            errorCode: 'VALIDATION_ERROR',
            errorMessage: `Validation failed: ${error}`,
            severity: 'ERROR'
          })
        }
      }
    }

    // Business warnings
    if (data.capacity && data.capacity > 1000) {
      warnings.push({
        field: 'capacity',
        value: data.capacity,
        warningCode: 'HIGH_CAPACITY',
        warningMessage: 'High capacity may impact performance'
      })
    }

    if (data.serviceFee && data.serviceFee > 1000) {
      warnings.push({
        field: 'serviceFee',
        value: data.serviceFee,
        warningCode: 'HIGH_SERVICE_FEE',
        warningMessage: 'High service fee may deter customers'
      })
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  // ===== BULK OPERATIONS =====

  /**
   * Bulk create stores with validation and rollback
   */
  static async bulkCreateStores(data: BulkStoreCreateData): Promise<BulkStoreOperation> {
    const operationId = `bulk_create_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const operation: BulkStoreOperation = {
      operationId,
      operationType: 'CREATE',
      totalItems: data.stores.length,
      processedItems: 0,
      successfulItems: 0,
      failedItems: 0,
      errors: [],
      status: 'PROCESSING',
      startedAt: new Date()
    }

    if (data.validateOnly) {
      // Validation-only mode
      for (let i = 0; i < data.stores.length; i++) {
        const storeData = data.stores[i]
        try {
          const validation = await this.validateStoreData(storeData)
          if (!validation.isValid) {
            operation.errors.push({
              itemIndex: i,
              itemData: storeData,
              errorCode: 'VALIDATION_FAILED',
              errorMessage: validation.errors.map(e => e.errorMessage).join(', ')
            })
            operation.failedItems++
          } else {
            operation.successfulItems++
          }
        } catch (error) {
          operation.errors.push({
            itemIndex: i,
            itemData: storeData,
            errorCode: 'VALIDATION_ERROR',
            errorMessage: `Validation error: ${error}`
          })
          operation.failedItems++
        }
        operation.processedItems++
      }
      operation.status = operation.failedItems > 0 ? 'FAILED' : 'COMPLETED'
      operation.completedAt = new Date()
      return operation
    }

    // Actual creation with transaction
    const createdStores: any[] = []

    try {
      await prisma.$transaction(async (tx) => {
        for (let i = 0; i < data.stores.length; i++) {
          const storeData = data.stores[i]

          try {
            // Validate before creating
            const validation = await this.validateStoreData(storeData)
            if (!validation.isValid) {
              const error: BulkOperationError = {
                itemIndex: i,
                itemData: storeData,
                errorCode: 'VALIDATION_FAILED',
                errorMessage: validation.errors.map(e => e.errorMessage).join(', ')
              }
              operation.errors.push(error)
              operation.failedItems++

              if (!data.continueOnError) {
                throw new Error(`Validation failed for item ${i}: ${error.errorMessage}`)
              }
              continue
            }

            // Create store
            const store = await tx.storeCode.create({
              data: {
                code: storeData.code.trim().toUpperCase(),
                name: storeData.name?.trim() || null,
                // Add other fields as needed, avoiding schema conflicts
                address: storeData.address?.trim() || null,
                city: storeData.city?.trim() || null,
                state: storeData.state?.trim() || null,
                postalCode: storeData.postalCode?.trim() || null,
                country: storeData.country?.trim() || 'Philippines',
                phone: storeData.phone?.trim() || null,
                website: storeData.website?.trim() || null,
                managerName: storeData.managerName?.trim() || null,
                managerPhone: storeData.managerPhone?.trim() || null,
                contactPerson: storeData.contactPerson?.trim() || null,
                operatingHours: storeData.operatingHours?.trim() || null,
                timezone: storeData.timezone?.trim() || 'Asia/Manila',
                isOpen: storeData.isOpen ?? true,
                allowsPickup: storeData.allowsPickup ?? true,
                allowsDelivery: storeData.allowsDelivery ?? true,
                deliveryRadius: storeData.deliveryRadius || null,
                minimumOrder: storeData.minimumOrder || 0,
                serviceFee: storeData.serviceFee || 0,
                averageProcessingTime: storeData.averageProcessingTime || null,
                capacity: storeData.capacity || null,
                notes: storeData.notes?.trim() || null,
                internalNotes: storeData.internalNotes?.trim() || null,
                specialInstructions: storeData.specialInstructions?.trim() || null,
                externalStoreId: storeData.externalStoreId?.trim() || null,
                apiEndpoint: storeData.apiEndpoint?.trim() || null,
                apiKey: storeData.apiKey?.trim() || null,
              }
            })

            createdStores.push(store)
            operation.successfulItems++
          } catch (error) {
            const bulkError: BulkOperationError = {
              itemIndex: i,
              itemData: storeData,
              errorCode: 'CREATE_FAILED',
              errorMessage: `Failed to create store: ${error}`
            }
            operation.errors.push(bulkError)
            operation.failedItems++

            if (!data.continueOnError) {
              throw error
            }
          }
          operation.processedItems++
        }
      })

      operation.status = operation.failedItems > 0 ? 'COMPLETED' : 'COMPLETED'
    } catch (error) {
      operation.status = 'FAILED'
      if (operation.errors.length === 0) {
        operation.errors.push({
          itemIndex: -1,
          errorCode: 'TRANSACTION_FAILED',
          errorMessage: `Transaction failed: ${error}`
        })
      }
    }

    operation.completedAt = new Date()
    return operation
  }

  /**
   * Bulk update stores
   */
  static async bulkUpdateStores(data: BulkStoreUpdateData): Promise<BulkStoreOperation> {
    const operationId = `bulk_update_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const operation: BulkStoreOperation = {
      operationId,
      operationType: 'UPDATE',
      totalItems: data.updates.length,
      processedItems: 0,
      successfulItems: 0,
      failedItems: 0,
      errors: [],
      status: 'PROCESSING',
      startedAt: new Date()
    }

    if (data.validateOnly) {
      // Validation-only mode
      for (let i = 0; i < data.updates.length; i++) {
        const { storeId, data: updateData } = data.updates[i]
        try {
          // Check if store exists
          const existingStore = await prisma.storeCode.findUnique({ where: { id: storeId } })
          if (!existingStore) {
            operation.errors.push({
              itemIndex: i,
              itemData: { storeId, ...updateData },
              errorCode: 'STORE_NOT_FOUND',
              errorMessage: `Store with ID ${storeId} not found`
            })
            operation.failedItems++
            continue
          }

          const validation = await this.validateStoreData({ ...updateData, id: storeId } as any, true)
          if (!validation.isValid) {
            operation.errors.push({
              itemIndex: i,
              itemData: { storeId, ...updateData },
              errorCode: 'VALIDATION_FAILED',
              errorMessage: validation.errors.map(e => e.errorMessage).join(', ')
            })
            operation.failedItems++
          } else {
            operation.successfulItems++
          }
        } catch (error) {
          operation.errors.push({
            itemIndex: i,
            itemData: { storeId, ...updateData },
            errorCode: 'VALIDATION_ERROR',
            errorMessage: `Validation error: ${error}`
          })
          operation.failedItems++
        }
        operation.processedItems++
      }
      operation.status = operation.failedItems > 0 ? 'FAILED' : 'COMPLETED'
      operation.completedAt = new Date()
      return operation
    }

    // Actual updates with transaction
    try {
      await prisma.$transaction(async (tx) => {
        for (let i = 0; i < data.updates.length; i++) {
          const { storeId, data: updateData } = data.updates[i]

          try {
            // Check if store exists
            const existingStore = await tx.storeCode.findUnique({ where: { id: storeId } })
            if (!existingStore) {
              const error: BulkOperationError = {
                itemIndex: i,
                itemData: { storeId, ...updateData },
                errorCode: 'STORE_NOT_FOUND',
                errorMessage: `Store with ID ${storeId} not found`
              }
              operation.errors.push(error)
              operation.failedItems++

              if (!data.continueOnError) {
                throw new Error(error.errorMessage)
              }
              continue
            }

            // Validate update data - include store ID for uniqueness checks
            const validation = await this.validateStoreData({ ...updateData, id: storeId } as any, true)
            if (!validation.isValid) {
              const error: BulkOperationError = {
                itemIndex: i,
                itemData: { storeId, ...updateData },
                errorCode: 'VALIDATION_FAILED',
                errorMessage: validation.errors.map(e => e.errorMessage).join(', ')
              }
              operation.errors.push(error)
              operation.failedItems++

              if (!data.continueOnError) {
                throw new Error(error.errorMessage)
              }
              continue
            }

            // Update store
            await tx.storeCode.update({
              where: { id: storeId },
              data: {
                ...updateData,
                code: updateData.code?.trim().toUpperCase(),
                name: updateData.name?.trim(),
                // Handle other fields safely
              }
            })

            operation.successfulItems++
          } catch (error) {
            const bulkError: BulkOperationError = {
              itemIndex: i,
              itemData: { storeId, ...updateData },
              errorCode: 'UPDATE_FAILED',
              errorMessage: `Failed to update store: ${error}`
            }
            operation.errors.push(bulkError)
            operation.failedItems++

            if (!data.continueOnError) {
              throw error
            }
          }
          operation.processedItems++
        }
      })

      operation.status = 'COMPLETED'
    } catch (error) {
      operation.status = 'FAILED'
      if (operation.errors.length === 0) {
        operation.errors.push({
          itemIndex: -1,
          errorCode: 'TRANSACTION_FAILED',
          errorMessage: `Transaction failed: ${error}`
        })
      }
    }

    operation.completedAt = new Date()
    return operation
  }

  /**
   * Bulk delete stores with dependency checks
   */
  static async bulkDeleteStores(data: BulkStoreDeleteData): Promise<BulkStoreOperation> {
    const operationId = `bulk_delete_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
    const operation: BulkStoreOperation = {
      operationId,
      operationType: 'DELETE',
      totalItems: data.storeIds.length,
      processedItems: 0,
      successfulItems: 0,
      failedItems: 0,
      errors: [],
      status: 'PROCESSING',
      startedAt: new Date()
    }

    if (data.validateOnly) {
      // Validation-only mode
      for (let i = 0; i < data.storeIds.length; i++) {
        const storeId = data.storeIds[i]
        try {
          // Check if store exists
          const store = await prisma.storeCode.findUnique({
            where: { id: storeId },
            include: {
              orders: { take: 1 },
              configurations: { take: 1 }
            }
          })

          if (!store) {
            operation.errors.push({
              itemIndex: i,
              itemData: { storeId },
              errorCode: 'STORE_NOT_FOUND',
              errorMessage: `Store with ID ${storeId} not found`
            })
            operation.failedItems++
            continue
          }

          // Check dependencies unless force delete
          if (!data.force) {
            if (store.orders.length > 0) {
              operation.errors.push({
                itemIndex: i,
                itemData: { storeId },
                errorCode: 'HAS_ORDERS',
                errorMessage: `Store has associated orders and cannot be deleted`
              })
              operation.failedItems++
              continue
            }
          }

          operation.successfulItems++
        } catch (error) {
          operation.errors.push({
            itemIndex: i,
            itemData: { storeId },
            errorCode: 'VALIDATION_ERROR',
            errorMessage: `Validation error: ${error}`
          })
          operation.failedItems++
        }
        operation.processedItems++
      }
      operation.status = operation.failedItems > 0 ? 'FAILED' : 'COMPLETED'
      operation.completedAt = new Date()
      return operation
    }

    // Actual deletion with transaction
    try {
      await prisma.$transaction(async (tx) => {
        for (let i = 0; i < data.storeIds.length; i++) {
          const storeId = data.storeIds[i]

          try {
            // Check if store exists
            const store = await tx.storeCode.findUnique({
              where: { id: storeId },
              include: {
                orders: { take: 1 },
                configurations: true
              }
            })

            if (!store) {
              const error: BulkOperationError = {
                itemIndex: i,
                itemData: { storeId },
                errorCode: 'STORE_NOT_FOUND',
                errorMessage: `Store with ID ${storeId} not found`
              }
              operation.errors.push(error)
              operation.failedItems++
              continue
            }

            // Check dependencies unless force delete
            if (!data.force && store.orders.length > 0) {
              const error: BulkOperationError = {
                itemIndex: i,
                itemData: { storeId },
                errorCode: 'HAS_ORDERS',
                errorMessage: `Store has associated orders and cannot be deleted`
              }
              operation.errors.push(error)
              operation.failedItems++
              continue
            }

            // Delete configurations first (cascade should handle this, but being explicit)
            if (store.configurations.length > 0) {
              await tx.storeConfiguration.deleteMany({
                where: { storeCodeId: storeId }
              })
            }

            // Delete the store
            await tx.storeCode.delete({
              where: { id: storeId }
            })

            operation.successfulItems++
          } catch (error) {
            const bulkError: BulkOperationError = {
              itemIndex: i,
              itemData: { storeId },
              errorCode: 'DELETE_FAILED',
              errorMessage: `Failed to delete store: ${error}`
            }
            operation.errors.push(bulkError)
            operation.failedItems++
          }
          operation.processedItems++
        }
      })

      operation.status = 'COMPLETED'
    } catch (error) {
      operation.status = 'FAILED'
      if (operation.errors.length === 0) {
        operation.errors.push({
          itemIndex: -1,
          errorCode: 'TRANSACTION_FAILED',
          errorMessage: `Transaction failed: ${error}`
        })
      }
    }

    operation.completedAt = new Date()
    return operation
  }

  // ===== IMPORT/EXPORT =====

  /**
   * Import stores from CSV/Excel data
   */
  static async importStores(fileBuffer: Buffer, filename: string): Promise<StoreImportResult> {
    const operationId = `import_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
    const result: StoreImportResult = {
      operationId,
      totalRows: 0,
      processedRows: 0,
      successfulRows: 0,
      failedRows: 0,
      errors: [],
      warnings: [],
      createdStores: [],
      updatedStores: [],
      skippedRows: []
    }

    try {
      // Parse file based on extension
      let workbook: XLSX.WorkBook
      const fileExtension = filename.toLowerCase().split('.').pop()

      if (fileExtension === 'csv') {
        const csvData = fileBuffer.toString('utf-8')
        workbook = XLSX.read(csvData, { type: 'string' })
      } else if (['xlsx', 'xls'].includes(fileExtension || '')) {
        workbook = XLSX.read(fileBuffer, { type: 'buffer' })
      } else {
        throw new Error('Unsupported file format. Please use CSV or Excel files.')
      }

      const worksheet = workbook.Sheets[workbook.SheetNames[0]]
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as any[][]

      if (jsonData.length === 0) {
        throw new Error('File is empty or has no data')
      }

      // Get headers from first row
      const headers = jsonData[0].map((h: any) => String(h).toLowerCase().trim())
      const dataRows = jsonData.slice(1)
      result.totalRows = dataRows.length

      // Define expected column mappings
      const columnMappings: Record<string, string> = {
        'code': 'code',
        'store_code': 'code',
        'storecode': 'code',
        'name': 'name',
        'store_name': 'name',
        'storename': 'name',
        'type': 'storeType',
        'store_type': 'storeType',
        'storetype': 'storeType',
        'status': 'status',
        'address': 'address',
        'city': 'city',
        'state': 'state',
        'postal_code': 'postalCode',
        'postalcode': 'postalCode',
        'zip': 'postalCode',
        'country': 'country',
        'phone': 'phone',
        'email': 'email',
        'website': 'website',
        'manager_name': 'managerName',
        'managername': 'managerName',
        'manager': 'managerName',
        'manager_phone': 'managerPhone',
        'managerphone': 'managerPhone',
        'manager_email': 'managerEmail',
        'manageremail': 'managerEmail',
        'contact_person': 'contactPerson',
        'contactperson': 'contactPerson',
        'contact': 'contactPerson',
        'operating_hours': 'operatingHours',
        'operatinghours': 'operatingHours',
        'hours': 'operatingHours',
        'timezone': 'timezone',
        'is_open': 'isOpen',
        'isopen': 'isOpen',
        'open': 'isOpen',
        'allows_pickup': 'allowsPickup',
        'allowspickup': 'allowsPickup',
        'pickup': 'allowsPickup',
        'allows_delivery': 'allowsDelivery',
        'allowsdelivery': 'allowsDelivery',
        'delivery': 'allowsDelivery',
        'delivery_radius': 'deliveryRadius',
        'deliveryradius': 'deliveryRadius',
        'radius': 'deliveryRadius',
        'minimum_order': 'minimumOrder',
        'minimumorder': 'minimumOrder',
        'min_order': 'minimumOrder',
        'service_fee': 'serviceFee',
        'servicefee': 'serviceFee',
        'fee': 'serviceFee',
        'capacity': 'capacity',
        'priority': 'priority',
        'notes': 'notes',
        'internal_notes': 'internalNotes',
        'internalnotes': 'internalNotes',
        'special_instructions': 'specialInstructions',
        'specialinstructions': 'specialInstructions',
        'instructions': 'specialInstructions'
      }

      // Process each row
      for (let rowIndex = 0; rowIndex < dataRows.length; rowIndex++) {
        const row = dataRows[rowIndex]
        const actualRowNumber = rowIndex + 2 // +2 because Excel rows start at 1 and we skip header

        try {
          // Skip empty rows
          if (!row || row.every((cell: any) => !cell || String(cell).trim() === '')) {
            result.skippedRows.push(actualRowNumber)
            continue
          }

          // Map row data to store object
          const storeData: Record<string, unknown> = {}

          for (let colIndex = 0; colIndex < headers.length; colIndex++) {
            const header = headers[colIndex]
            const mappedField = columnMappings[header]
            const cellValue = row[colIndex]

            if (mappedField && cellValue !== undefined && cellValue !== null && String(cellValue).trim() !== '') {
              const value = String(cellValue).trim()

              // Type conversion based on field
              switch (mappedField) {
                case 'isOpen':
                case 'allowsPickup':
                case 'allowsDelivery':
                  storeData[mappedField as keyof EnhancedStoreData] = ['true', '1', 'yes', 'y'].includes(value.toLowerCase()) as any
                  break
                case 'deliveryRadius':
                case 'minimumOrder':
                case 'serviceFee':
                case 'capacity':
                case 'averageProcessingTime':
                  const numValue = parseFloat(value)
                  if (!isNaN(numValue)) {
                    storeData[mappedField as keyof EnhancedStoreData] = numValue as any
                  }
                  break
                case 'storeType':
                  const validTypes = ['RETAIL', 'WHOLESALE', 'ONLINE', 'MARKETPLACE', 'WAREHOUSE', 'DISTRIBUTION_CENTER', 'FRANCHISE', 'CORPORATE']
                  if (validTypes.includes(value.toUpperCase())) {
                    storeData[mappedField] = value.toUpperCase() as any
                  }
                  break
                case 'status':
                  const validStatuses = ['ACTIVE', 'INACTIVE', 'MAINTENANCE', 'TEMPORARILY_CLOSED', 'PERMANENTLY_CLOSED', 'UNDER_CONSTRUCTION']
                  if (validStatuses.includes(value.toUpperCase())) {
                    storeData[mappedField] = value.toUpperCase() as any
                  }
                  break
                case 'priority':
                  const validPriorities = ['LOW', 'NORMAL', 'HIGH', 'PREMIUM']
                  if (validPriorities.includes(value.toUpperCase())) {
                    storeData[mappedField] = value.toUpperCase() as any
                  }
                  break
                default:
                  storeData[mappedField as keyof EnhancedStoreData] = value as any
              }
            }
          }

          // Validate required fields
          if (!storeData.code) {
            result.errors.push({
              row: actualRowNumber,
              field: 'code',
              errorCode: 'REQUIRED_FIELD_MISSING',
              errorMessage: 'Store code is required'
            })
            result.failedRows++
            continue
          }

          // Validate the store data
          const validation = await this.validateStoreData(storeData)
          if (!validation.isValid) {
            result.errors.push({
              row: actualRowNumber,
              errorCode: 'VALIDATION_FAILED',
              errorMessage: validation.errors.map(e => e.errorMessage).join(', ')
            })
            result.failedRows++
            continue
          }

          // Add warnings
          validation.warnings.forEach(warning => {
            result.warnings.push({
              row: actualRowNumber,
              field: warning.field,
              value: warning.value,
              warningCode: warning.warningCode,
              warningMessage: warning.warningMessage
            })
          })

          // Check if store already exists (for update vs create)
          const existingStore = await prisma.storeCode.findFirst({
            where: { code: String(storeData.code).toUpperCase() }
          })

          // Helper function to safely get string values
          const getString = (key: string): string | null => {
            const value = storeData[key]
            return value ? String(value).trim() : null
          }

          // Helper function to safely get number values
          const getNumber = (key: string): number | null => {
            const value = storeData[key]
            return value ? Number(value) : null
          }

          // Helper function to safely get boolean values
          const getBoolean = (key: string, defaultValue = true): boolean => {
            const value = storeData[key]
            return value !== undefined ? Boolean(value) : defaultValue
          }

          if (existingStore) {
            // Update existing store
            const updatedStore = await prisma.storeCode.update({
              where: { id: existingStore.id },
              data: {
                name: getString('name'),
                address: getString('address'),
                city: getString('city'),
                state: getString('state'),
                postalCode: getString('postalCode'),
                country: getString('country') || 'Philippines',
                phone: getString('phone'),
                website: getString('website'),
                managerName: getString('managerName'),
                managerPhone: getString('managerPhone'),
                contactPerson: getString('contactPerson'),
                operatingHours: getString('operatingHours'),
                timezone: getString('timezone') || 'Asia/Manila',
                isOpen: getBoolean('isOpen'),
                allowsPickup: getBoolean('allowsPickup'),
                allowsDelivery: getBoolean('allowsDelivery'),
                deliveryRadius: getNumber('deliveryRadius'),
                minimumOrder: getNumber('minimumOrder') || 0,
                serviceFee: getNumber('serviceFee') || 0,
                averageProcessingTime: getNumber('averageProcessingTime'),
                capacity: getNumber('capacity'),
                notes: getString('notes'),
                internalNotes: getString('internalNotes'),
                specialInstructions: getString('specialInstructions'),
              }
            })
            result.updatedStores.push(updatedStore)
          } else {
            // Create new store
            const newStore = await prisma.storeCode.create({
              data: {
                code: String(storeData.code).trim().toUpperCase(),
                name: getString('name'),
                address: getString('address'),
                city: getString('city'),
                state: getString('state'),
                postalCode: getString('postalCode'),
                country: getString('country') || 'Philippines',
                phone: getString('phone'),
                website: getString('website'),
                managerName: getString('managerName'),
                managerPhone: getString('managerPhone'),
                contactPerson: getString('contactPerson'),
                operatingHours: getString('operatingHours'),
                timezone: getString('timezone') || 'Asia/Manila',
                isOpen: getBoolean('isOpen'),
                allowsPickup: getBoolean('allowsPickup'),
                allowsDelivery: getBoolean('allowsDelivery'),
                deliveryRadius: getNumber('deliveryRadius'),
                minimumOrder: getNumber('minimumOrder') || 0,
                serviceFee: getNumber('serviceFee') || 0,
                averageProcessingTime: getNumber('averageProcessingTime'),
                capacity: getNumber('capacity'),
                notes: getString('notes'),
                internalNotes: getString('internalNotes'),
                specialInstructions: getString('specialInstructions'),
              }
            })
            result.createdStores.push(newStore)
          }

          result.successfulRows++
        } catch (error) {
          result.errors.push({
            row: actualRowNumber,
            errorCode: 'PROCESSING_ERROR',
            errorMessage: `Error processing row: ${error}`
          })
          result.failedRows++
        }

        result.processedRows++
      }

    } catch (error) {
      result.errors.push({
        row: 0,
        errorCode: 'FILE_PROCESSING_ERROR',
        errorMessage: `Error processing file: ${error}`
      })
    }

    return result
  }

  /**
   * Export stores to CSV/Excel format
   */
  static async exportStores(options: StoreExportOptions): Promise<Buffer> {
    // Get stores based on filters
    const searchResult = await this.searchStores(
      options.filters || {},
      { page: 1, limit: 10000 }, // Large limit for export
      { field: 'code', direction: 'asc' }
    )

    const stores = searchResult.stores

    // Define export fields
    const defaultFields = [
      'code', 'name', 'storeType', 'status', 'address', 'city', 'state',
      'postalCode', 'country', 'phone', 'email', 'website', 'managerName',
      'managerPhone', 'contactPerson', 'operatingHours', 'timezone',
      'isOpen', 'allowsPickup', 'allowsDelivery', 'deliveryRadius',
      'minimumOrder', 'serviceFee', 'capacity', 'priority', 'notes'
    ]

    const fieldsToExport = options.fields || defaultFields

    // Prepare data for export
    const exportData = stores.map(store => {
      const row: any = {}

      fieldsToExport.forEach(field => {
        switch (field) {
          case 'code':
            row['Store Code'] = store.code
            break
          case 'name':
            row['Store Name'] = store.name || ''
            break
          case 'storeType':
            row['Store Type'] = store.storeType || ''
            break
          case 'status':
            row['Status'] = store.status || ''
            break
          case 'address':
            row['Address'] = store.address || ''
            break
          case 'city':
            row['City'] = store.city || ''
            break
          case 'state':
            row['State'] = store.state || ''
            break
          case 'postalCode':
            row['Postal Code'] = store.postalCode || ''
            break
          case 'country':
            row['Country'] = store.country || ''
            break
          case 'phone':
            row['Phone'] = store.phone || ''
            break
          case 'email':
            row['Email'] = store.email || ''
            break
          case 'website':
            row['Website'] = store.website || ''
            break
          case 'managerName':
            row['Manager Name'] = store.managerName || ''
            break
          case 'managerPhone':
            row['Manager Phone'] = store.managerPhone || ''
            break
          case 'contactPerson':
            row['Contact Person'] = store.contactPerson || ''
            break
          case 'operatingHours':
            row['Operating Hours'] = store.operatingHours || ''
            break
          case 'timezone':
            row['Timezone'] = store.timezone || ''
            break
          case 'isOpen':
            row['Is Open'] = store.isOpen ? 'Yes' : 'No'
            break
          case 'allowsPickup':
            row['Allows Pickup'] = store.allowsPickup ? 'Yes' : 'No'
            break
          case 'allowsDelivery':
            row['Allows Delivery'] = store.allowsDelivery ? 'Yes' : 'No'
            break
          case 'deliveryRadius':
            row['Delivery Radius (km)'] = store.deliveryRadius || ''
            break
          case 'minimumOrder':
            row['Minimum Order'] = store.minimumOrder || ''
            break
          case 'serviceFee':
            row['Service Fee'] = store.serviceFee || ''
            break
          case 'capacity':
            row['Capacity'] = store.capacity || ''
            break
          case 'priority':
            row['Priority'] = store.priority || ''
            break
          case 'notes':
            row['Notes'] = store.notes || ''
            break
        }
      })

      // Add metrics if requested
      if (options.includeMetrics) {
        row['Total Orders'] = store.totalOrders || 0
        row['Total Revenue'] = store.totalRevenue || 0
        row['Average Order Value'] = store.averageOrderValue || 0
      }

      // Add configurations if requested
      if (options.includeConfigurations && store.configurations) {
        const configs = store.configurations
          .filter((config: any) => config.isActive)
          .map((config: any) => `${config.configKey}=${config.configValue}`)
          .join('; ')
        row['Configurations'] = configs
      }

      return row
    })

    // Create workbook
    const workbook = XLSX.utils.book_new()
    const worksheet = XLSX.utils.json_to_sheet(exportData)

    // Auto-size columns
    const colWidths = Object.keys(exportData[0] || {}).map(key => ({
      wch: Math.max(key.length, 15)
    }))
    worksheet['!cols'] = colWidths

    XLSX.utils.book_append_sheet(workbook, worksheet, 'Stores')

    // Generate buffer based on format
    if (options.format === 'CSV') {
      const csvData = XLSX.utils.sheet_to_csv(worksheet)
      return Buffer.from(csvData, 'utf-8')
    } else {
      return Buffer.from(XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' }))
    }
  }

  /**
   * Generate import template
   */
  static generateImportTemplate(format: 'CSV' | 'EXCEL' = 'EXCEL'): Buffer {
    const headers = [
      'Code', 'Name', 'Type', 'Status', 'Address', 'City', 'State',
      'Postal Code', 'Country', 'Phone', 'Email', 'Website',
      'Manager Name', 'Manager Phone', 'Contact Person',
      'Operating Hours', 'Timezone', 'Is Open', 'Allows Pickup',
      'Allows Delivery', 'Delivery Radius', 'Minimum Order',
      'Service Fee', 'Capacity', 'Priority', 'Notes'
    ]

    const sampleData = [
      [
        'SM-MALL', 'SM Mall of Asia', 'RETAIL', 'ACTIVE',
        '123 Mall Street', 'Pasay', 'Metro Manila', '1300', 'Philippines',
        '+63-2-123-4567', '<EMAIL>', 'https://smmoa.com',
        'John Manager', '+63-************', 'Jane Contact',
        'Mon-Sun 10:00-22:00', 'Asia/Manila', 'Yes', 'Yes',
        'Yes', '10', '500', '50', '100', 'NORMAL', 'Sample store'
      ]
    ]

    const workbook = XLSX.utils.book_new()
    const worksheet = XLSX.utils.aoa_to_sheet([headers, ...sampleData])

    // Auto-size columns
    const colWidths = headers.map(header => ({
      wch: Math.max(header.length, 15)
    }))
    worksheet['!cols'] = colWidths

    XLSX.utils.book_append_sheet(workbook, worksheet, 'Store Import Template')

    if (format === 'CSV') {
      const csvData = XLSX.utils.sheet_to_csv(worksheet)
      return Buffer.from(csvData, 'utf-8')
    } else {
      return Buffer.from(XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' }))
    }
  }

  /**
   * Get bulk operation status
   */
  static async getBulkOperationStatus(operationId: string): Promise<BulkStoreOperation | null> {
    // In a real implementation, this would fetch from a database or cache
    // For now, return null as operations are processed synchronously
    return null
  }

  /**
   * Cancel bulk operation
   */
  static async cancelBulkOperation(operationId: string): Promise<boolean> {
    // In a real implementation, this would cancel an ongoing operation
    // For now, return false as operations are processed synchronously
    return false
  }
}
